<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>an-Q 移动端演示</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #5a9178;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .app-content {
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
            background: #f8f9fa;
        }

        .tab-bar {
            height: 80px;
            background: #fff;
            border-top: 1px solid #e1e1e1;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 20px;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s;
            padding: 8px;
        }

        .tab-item.active {
            color: #5a9178;
        }

        .tab-item .material-icons {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .tab-item span {
            font-size: 12px;
            font-weight: 500;
        }

        /* 首页样式 */
        .home-screen {
            display: none;
        }

        .home-screen.active {
            display: block;
        }

        .search-container {
            background: #5a9178;
            padding: 16px;
        }

        .search-bar {
            background: white;
            border-radius: 25px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .search-bar .material-icons {
            color: #999;
            margin-right: 8px;
        }

        .search-bar input {
            border: none;
            outline: none;
            flex: 1;
            font-size: 16px;
        }

        .banner {
            margin: 16px;
            height: 180px;
            background: linear-gradient(135deg, #5a9178, #4a7c59);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .banner-content {
            text-align: center;
            z-index: 2;
        }

        .banner h2 {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .banner p {
            font-size: 16px;
            opacity: 0.9;
        }

        .quick-actions {
            background: white;
            margin: 16px;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }

        .action-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .action-item:hover {
            transform: scale(1.05);
        }

        .action-icon {
            width: 48px;
            height: 48px;
            background: #f0f8f5;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
        }

        .action-icon .material-icons {
            color: #5a9178;
            font-size: 24px;
        }

        .action-item span {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .action-item small {
            font-size: 12px;
            color: #666;
        }

        .section {
            margin: 16px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .more-text {
            font-size: 14px;
            color: #5a9178;
            cursor: pointer;
        }

        .package-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .package-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .package-card:hover {
            transform: translateY(-2px);
        }

        .package-image {
            height: 120px;
            background: linear-gradient(135deg, #5a9178, #6ba085);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            position: relative;
        }

        .level-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255,255,255,0.9);
            color: #5a9178;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .package-content {
            padding: 12px;
        }

        .package-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 6px;
            line-height: 1.2;
        }

        .package-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .package-sessions {
            font-size: 12px;
            color: #666;
        }

        .package-price {
            font-size: 18px;
            font-weight: bold;
            color: #f5222d;
        }

        /* 其他屏幕样式 */
        .screen {
            display: none;
            height: 100%;
            padding: 20px;
            text-align: center;
        }

        .screen.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .screen h2 {
            font-size: 24px;
            color: #333;
            margin-bottom: 16px;
        }

        .screen p {
            font-size: 16px;
            color: #666;
        }

        .loading {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #5a9178;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-info {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .phone-container {
                width: 100%;
                max-width: 375px;
                height: 100vh;
                border-radius: 0;
                padding: 0;
            }

            .phone-screen {
                border-radius: 0;
            }

            .demo-info {
                position: relative;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-info">
        📱 Shuan-Q 移动端演示 - 点击底部Tab切换页面
    </div>

    <div class="phone-container">
        <div class="phone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>Shuan-Q</span>
                <span>100%</span>
            </div>

            <!-- 应用内容区域 -->
            <div class="app-content">
                <!-- 首页 -->
                <div class="home-screen active" id="home">
                    <!-- 搜索栏 -->
                    <div class="search-container">
                        <div class="search-bar">
                            <i class="material-icons">search</i>
                            <input type="text" placeholder="搜索课程包、教练...">
                        </div>
                    </div>

                    <!-- Banner -->
                    <div class="banner">
                        <div class="banner-content">
                            <h2>专业台球教学</h2>
                            <p>一对一精品课程</p>
                        </div>
                    </div>

                    <!-- 快速入口 -->
                    <div class="quick-actions">
                        <div class="actions-grid">
                            <div class="action-item" onclick="switchTab('coach')">
                                <div class="action-icon">
                                    <i class="material-icons">person</i>
                                </div>
                                <span>找教练</span>
                                <small>专业教练</small>
                            </div>
                            <div class="action-item" onclick="switchTab('mall')">
                                <div class="action-icon">
                                    <i class="material-icons">school</i>
                                </div>
                                <span>课程包</span>
                                <small>精品课程</small>
                            </div>
                            <div class="action-item" onclick="switchTab('profile')">
                                <div class="action-icon">
                                    <i class="material-icons">event</i>
                                </div>
                                <span>我的预约</span>
                                <small>预约管理</small>
                            </div>
                            <div class="action-item" onclick="switchTab('profile')">
                                <div class="action-icon">
                                    <i class="material-icons">shopping_bag</i>
                                </div>
                                <span>我的订单</span>
                                <small>订单查询</small>
                            </div>
                        </div>
                    </div>

                    <!-- 热门课程包 -->
                    <div class="section">
                        <div class="section-header">
                            <h3 class="section-title">热门课程包</h3>
                            <span class="more-text" onclick="switchTab('mall')">更多 →</span>
                        </div>
                        <div class="package-grid">
                            <div class="package-card">
                                <div class="package-image">
                                    <span>基础入门</span>
                                    <div class="level-badge">初级</div>
                                </div>
                                <div class="package-content">
                                    <div class="package-name">台球基础入门课程</div>
                                    <div class="package-info">
                                        <span class="package-sessions">8课时 · 30天</span>
                                    </div>
                                    <div class="package-price">¥299</div>
                                </div>
                            </div>
                            <div class="package-card">
                                <div class="package-image">
                                    <span>进阶提升</span>
                                    <div class="level-badge">中级</div>
                                </div>
                                <div class="package-content">
                                    <div class="package-name">台球技巧提升训练</div>
                                    <div class="package-info">
                                        <span class="package-sessions">12课时 · 45天</span>
                                    </div>
                                    <div class="package-price">¥499</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商城页面 -->
                <div class="screen" id="mall">
                    <!-- 搜索栏 -->
                    <div class="search-container">
                        <div class="search-bar">
                            <i class="material-icons">search</i>
                            <input type="text" placeholder="搜索课程包...">
                        </div>
                    </div>

                    <!-- 分类选择 -->
                    <div style="background: white; padding: 16px; margin-bottom: 12px;">
                        <div style="display: flex; gap: 16px; overflow-x: auto;">
                            <div style="text-align: center; min-width: 60px;">
                                <div style="width: 48px; height: 48px; background: #5a9178; border-radius: 24px; display: flex; align-items: center; justify-content: center; margin: 0 auto 8px;">
                                    <i class="material-icons" style="color: white;">apps</i>
                                </div>
                                <span style="font-size: 12px; color: #5a9178; font-weight: 600;">全部</span>
                            </div>
                            <div style="text-align: center; min-width: 60px;">
                                <div style="width: 48px; height: 48px; background: #f0f8f5; border-radius: 24px; display: flex; align-items: center; justify-content: center; margin: 0 auto 8px;">
                                    <i class="material-icons" style="color: #5a9178;">school</i>
                                </div>
                                <span style="font-size: 12px; color: #333;">基础</span>
                            </div>
                            <div style="text-align: center; min-width: 60px;">
                                <div style="width: 48px; height: 48px; background: #f0f8f5; border-radius: 24px; display: flex; align-items: center; justify-content: center; margin: 0 auto 8px;">
                                    <i class="material-icons" style="color: #5a9178;">trending_up</i>
                                </div>
                                <span style="font-size: 12px; color: #333;">进阶</span>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选栏 -->
                    <div style="background: white; padding: 12px 16px; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #e1e1e1;">
                        <div style="display: flex; align-items: center; border: 1px solid #5a9178; border-radius: 16px; padding: 6px 12px; cursor: pointer;">
                            <i class="material-icons" style="color: #5a9178; font-size: 20px; margin-right: 4px;">filter_list</i>
                            <span style="color: #5a9178; font-size: 14px;">筛选</span>
                        </div>
                        <span style="color: #666; font-size: 14px;">共 12 个课程包</span>
                    </div>

                    <!-- 商品网格 -->
                    <div style="padding: 16px;">
                        <div class="package-grid">
                            <div class="package-card">
                                <div class="package-image">
                                    <span>斯诺克入门</span>
                                    <div class="level-badge">初级</div>
                                </div>
                                <div class="package-content">
                                    <div class="package-name">斯诺克基础教学</div>
                                    <div class="package-info">
                                        <span class="package-sessions">10课时 · 30天</span>
                                    </div>
                                    <div class="package-price">¥399</div>
                                </div>
                            </div>
                            <div class="package-card">
                                <div class="package-image">
                                    <span>九球技巧</span>
                                    <div class="level-badge">中级</div>
                                </div>
                                <div class="package-content">
                                    <div class="package-name">九球进阶训练</div>
                                    <div class="package-info">
                                        <span class="package-sessions">15课时 · 45天</span>
                                    </div>
                                    <div class="package-price">¥599</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 教练页面 -->
                <div class="screen" id="coach">
                    <!-- 搜索栏 -->
                    <div class="search-container">
                        <div class="search-bar">
                            <i class="material-icons">search</i>
                            <input type="text" placeholder="搜索教练...">
                        </div>
                    </div>

                    <!-- 筛选栏 -->
                    <div style="background: white; padding: 12px 16px; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #e1e1e1;">
                        <div style="display: flex; align-items: center; border: 1px solid #5a9178; border-radius: 16px; padding: 6px 12px; cursor: pointer;">
                            <i class="material-icons" style="color: #5a9178; font-size: 20px; margin-right: 4px;">filter_list</i>
                            <span style="color: #5a9178; font-size: 14px;">筛选</span>
                        </div>
                        <span style="color: #666; font-size: 14px;">共 8 位教练</span>
                    </div>

                    <!-- 教练列表 -->
                    <div style="padding: 16px;">
                        <div style="background: white; border-radius: 12px; padding: 16px; margin-bottom: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                <div style="width: 56px; height: 56px; background: #f0f8f5; border-radius: 28px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                    <i class="material-icons" style="color: #5a9178; font-size: 32px;">person</i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 4px;">张教练</div>
                                    <div style="display: flex; align-items: center;">
                                        <i class="material-icons" style="color: #faad14; font-size: 16px;">star</i>
                                        <span style="color: #333; margin-left: 4px; margin-right: 8px;">4.9</span>
                                        <span style="color: #666;">5年经验</span>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 20px; font-weight: bold; color: #f5222d;">¥150</div>
                                    <div style="font-size: 12px; color: #666;">/小时</div>
                                </div>
                            </div>
                            <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                                <span style="background: #f0f8f5; color: #5a9178; padding: 4px 8px; border-radius: 12px; font-size: 12px;">斯诺克</span>
                                <span style="background: #f0f8f5; color: #5a9178; padding: 4px 8px; border-radius: 12px; font-size: 12px;">九球</span>
                            </div>
                            <div style="color: #666; font-size: 14px; line-height: 1.4;">专业台球教练，擅长基础教学和技巧提升...</div>
                        </div>

                        <div style="background: white; border-radius: 12px; padding: 16px; margin-bottom: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                <div style="width: 56px; height: 56px; background: #f0f8f5; border-radius: 28px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                    <i class="material-icons" style="color: #5a9178; font-size: 32px;">person</i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 4px;">李教练</div>
                                    <div style="display: flex; align-items: center;">
                                        <i class="material-icons" style="color: #faad14; font-size: 16px;">star</i>
                                        <span style="color: #333; margin-left: 4px; margin-right: 8px;">4.8</span>
                                        <span style="color: #666;">3年经验</span>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 20px; font-weight: bold; color: #f5222d;">¥120</div>
                                    <div style="font-size: 12px; color: #666;">/小时</div>
                                </div>
                            </div>
                            <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                                <span style="background: #f0f8f5; color: #5a9178; padding: 4px 8px; border-radius: 12px; font-size: 12px;">基础教学</span>
                                <span style="background: #f0f8f5; color: #5a9178; padding: 4px 8px; border-radius: 12px; font-size: 12px;">中式八球</span>
                            </div>
                            <div style="color: #666; font-size: 14px; line-height: 1.4;">耐心细致，适合初学者入门学习...</div>
                        </div>
                    </div>
                </div>

                <!-- 个人中心页面 -->
                <div class="screen" id="profile">
                    <!-- 用户信息头部 -->
                    <div style="background: white; padding: 20px; margin-bottom: 12px;">
                        <div style="display: flex; align-items: center;">
                            <div style="width: 64px; height: 64px; background: #f0f8f5; border-radius: 32px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                                <i class="material-icons" style="color: #5a9178; font-size: 40px;">person</i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: 20px; font-weight: bold; color: #333; margin-bottom: 4px;">张同学</div>
                                <div style="font-size: 14px; color: #666; margin-bottom: 8px;">138****8888</div>
                                <div style="background: #5a9178; color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px; font-weight: 600; display: inline-block;">学员</div>
                            </div>
                            <i class="material-icons" style="color: #5a9178; cursor: pointer;">edit</i>
                        </div>
                    </div>

                    <!-- 学员功能菜单 -->
                    <div style="background: white; margin-bottom: 12px;">
                        <div style="padding: 12px 20px; background: #f8f9fa; font-size: 14px; color: #999;">我的服务</div>
                        <div style="padding: 16px 20px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; cursor: pointer;">
                            <div style="width: 40px; height: 40px; background: rgba(245, 34, 45, 0.1); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                                <i class="material-icons" style="color: #f5222d; font-size: 24px;">shopping_bag</i>
                            </div>
                            <span style="flex: 1; font-size: 16px; color: #333;">我的订单</span>
                            <i class="material-icons" style="color: #999;">chevron_right</i>
                        </div>
                        <div style="padding: 16px 20px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; cursor: pointer;">
                            <div style="width: 40px; height: 40px; background: rgba(82, 196, 26, 0.1); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                                <i class="material-icons" style="color: #52c41a; font-size: 24px;">event</i>
                            </div>
                            <span style="flex: 1; font-size: 16px; color: #333;">我的预约</span>
                            <i class="material-icons" style="color: #999;">chevron_right</i>
                        </div>
                        <div style="padding: 16px 20px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; cursor: pointer;">
                            <div style="width: 40px; height: 40px; background: rgba(250, 173, 20, 0.1); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                                <i class="material-icons" style="color: #faad14; font-size: 24px;">favorite</i>
                            </div>
                            <span style="flex: 1; font-size: 16px; color: #333;">我的收藏</span>
                            <i class="material-icons" style="color: #999;">chevron_right</i>
                        </div>
                        <div style="padding: 16px 20px; display: flex; align-items: center; cursor: pointer;">
                            <div style="width: 40px; height: 40px; background: rgba(114, 46, 209, 0.1); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                                <i class="material-icons" style="color: #722ed1; font-size: 24px;">history</i>
                            </div>
                            <span style="flex: 1; font-size: 16px; color: #333;">学习记录</span>
                            <i class="material-icons" style="color: #999;">chevron_right</i>
                        </div>
                    </div>

                    <!-- 通用功能 -->
                    <div style="background: white; margin-bottom: 12px;">
                        <div style="padding: 12px 20px; background: #f8f9fa; font-size: 14px; color: #999;">通用功能</div>
                        <div style="padding: 16px 20px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; cursor: pointer;">
                            <div style="width: 40px; height: 40px; background: rgba(102, 102, 102, 0.1); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                                <i class="material-icons" style="color: #666; font-size: 24px;">edit</i>
                            </div>
                            <span style="flex: 1; font-size: 16px; color: #333;">编辑资料</span>
                            <i class="material-icons" style="color: #999;">chevron_right</i>
                        </div>
                        <div style="padding: 16px 20px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; cursor: pointer;">
                            <div style="width: 40px; height: 40px; background: rgba(102, 102, 102, 0.1); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                                <i class="material-icons" style="color: #666; font-size: 24px;">settings</i>
                            </div>
                            <span style="flex: 1; font-size: 16px; color: #333;">设置</span>
                            <i class="material-icons" style="color: #999;">chevron_right</i>
                        </div>
                        <div style="padding: 16px 20px; display: flex; align-items: center; cursor: pointer;">
                            <div style="width: 40px; height: 40px; background: rgba(102, 102, 102, 0.1); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                                <i class="material-icons" style="color: #666; font-size: 24px;">help</i>
                            </div>
                            <span style="flex: 1; font-size: 16px; color: #333;">帮助与反馈</span>
                            <i class="material-icons" style="color: #999;">chevron_right</i>
                        </div>
                    </div>

                    <!-- 退出登录 -->
                    <div style="background: white; margin-top: 12px; margin-bottom: 32px;">
                        <div style="padding: 16px 20px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                            <i class="material-icons" style="color: #f5222d; margin-right: 8px;">exit_to_app</i>
                            <span style="color: #f5222d; font-size: 16px;">退出登录</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部Tab栏 -->
            <div class="tab-bar">
                <div class="tab-item active" onclick="switchTab('home')">
                    <i class="material-icons">home</i>
                    <span>首页</span>
                </div>
                <div class="tab-item" onclick="switchTab('mall')">
                    <i class="material-icons">shopping_cart</i>
                    <span>商城</span>
                </div>
                <div class="tab-item" onclick="switchTab('coach')">
                    <i class="material-icons">person</i>
                    <span>教练</span>
                </div>
                <div class="tab-item" onclick="switchTab('profile')">
                    <i class="material-icons">account_circle</i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有屏幕
            document.querySelectorAll('.home-screen, .screen').forEach(screen => {
                screen.classList.remove('active');
            });

            // 移除所有tab的active状态
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示对应屏幕
            if (tabName === 'home') {
                document.getElementById('home').classList.add('active');
            } else {
                document.getElementById(tabName).classList.add('active');
            }

            // 激活对应tab
            document.querySelectorAll('.tab-item')[getTabIndex(tabName)].classList.add('active');

            // 添加切换动画效果
            document.querySelector('.app-content').style.transform = 'scale(0.95)';
            setTimeout(() => {
                document.querySelector('.app-content').style.transform = 'scale(1)';
            }, 100);
        }

        function getTabIndex(tabName) {
            const tabs = ['home', 'mall', 'coach', 'profile'];
            return tabs.indexOf(tabName);
        }

        // 模拟API调用
        function simulateApiCall() {
            console.log('🔗 模拟API调用: http://localhost:3000/api/packages');
            return new Promise(resolve => {
                setTimeout(() => {
                    resolve({
                        success: true,
                        data: {
                            packages: [
                                { id: 1, name: '台球基础入门', price: 299, level: 'beginner' },
                                { id: 2, name: '进阶技巧提升', price: 499, level: 'intermediate' }
                            ]
                        }
                    });
                }, 1000);
            });
        }

        // 添加点击效果
        function addClickEffect(element) {
            element.style.transform = 'scale(0.95)';
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 150);
        }

        // 模拟搜索功能
        function handleSearch(input) {
            console.log('🔍 搜索:', input.value);
            if (input.value.length > 0) {
                console.log('📊 搜索结果: 找到相关课程包和教练');
            }
        }

        // 模拟课程包点击
        function handlePackageClick(packageName) {
            console.log('📦 点击课程包:', packageName);
            alert(`查看课程包详情: ${packageName}\n\n这里会跳转到课程包详情页面，包含:\n• 详细介绍\n• 教练信息\n• 立即购买按钮\n• 用户评价`);
        }

        // 模拟教练点击
        function handleCoachClick(coachName) {
            console.log('👨‍🏫 点击教练:', coachName);
            alert(`查看教练详情: ${coachName}\n\n这里会跳转到教练详情页面，包含:\n• 教练简介\n• 专长领域\n• 课程包列表\n• 预约课程按钮`);
        }

        // 模拟功能点击
        function handleFeatureClick(featureName) {
            console.log('⚡ 点击功能:', featureName);
            alert(`${featureName}\n\n这个功能正在开发中...\n\n完整版本将包含:\n• 完整的业务流程\n• 数据持久化\n• 实时通知\n• 支付集成`);
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 Shuan-Q 移动端演示已加载');
            console.log('🔗 后端API: http://localhost:3000');
            console.log('💡 提示: 点击底部Tab切换页面，点击各种元素查看交互效果');

            // 添加搜索框事件
            document.querySelectorAll('input[type="text"]').forEach(input => {
                input.addEventListener('input', () => handleSearch(input));
            });

            // 添加课程包点击事件
            document.querySelectorAll('.package-card').forEach(card => {
                card.addEventListener('click', function() {
                    addClickEffect(this);
                    const packageName = this.querySelector('.package-name').textContent;
                    setTimeout(() => handlePackageClick(packageName), 150);
                });
            });

            // 添加教练卡片点击事件
            document.querySelectorAll('[style*="background: white"][style*="border-radius: 12px"][style*="padding: 16px"]').forEach(card => {
                if (card.querySelector('.material-icons[style*="font-size: 32px"]')) {
                    card.addEventListener('click', function() {
                        addClickEffect(this);
                        const coachName = this.querySelector('div[style*="font-size: 18px"]')?.textContent;
                        if (coachName && coachName.includes('教练')) {
                            setTimeout(() => handleCoachClick(coachName), 150);
                        }
                    });
                }
            });

            // 添加功能菜单点击事件
            document.querySelectorAll('[style*="cursor: pointer"]').forEach(item => {
                if (item.querySelector('span') && !item.classList.contains('package-card')) {
                    item.addEventListener('click', function() {
                        addClickEffect(this);
                        const featureName = this.querySelector('span')?.textContent;
                        if (featureName && !featureName.includes('→')) {
                            setTimeout(() => handleFeatureClick(featureName), 150);
                        }
                    });
                }
            });

            // 模拟数据加载
            simulateApiCall().then(data => {
                console.log('📊 API数据加载完成:', data);
            });
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            .app-content {
                transition: transform 0.2s ease;
            }

            .package-card, [style*="cursor: pointer"] {
                transition: transform 0.15s ease;
            }

            .package-card:hover, [style*="cursor: pointer"]:hover {
                transform: translateY(-2px);
            }

            .tab-item {
                transition: color 0.3s ease;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .screen.active, .home-screen.active {
                animation: fadeIn 0.3s ease;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
