<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shuan-Q API 测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        h1 {
            color: #5a9178;
            margin-bottom: 24px;
            text-align: center;
        }

        .api-section {
            margin-bottom: 32px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #5a9178;
        }

        .api-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 12px;
        }

        .api-url {
            background: #333;
            color: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            margin-bottom: 12px;
            word-break: break-all;
        }

        .test-button {
            background: #5a9178;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 12px;
            margin-bottom: 12px;
        }

        .test-button:hover {
            background: #4a7c59;
        }

        .result {
            background: #fff;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            padding: 12px;
            margin-top: 12px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .success {
            border-color: #52c41a;
            background: #f6ffed;
        }

        .error {
            border-color: #f5222d;
            background: #fff2f0;
        }

        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 12px;
        }

        .status.online {
            background: #f6ffed;
            color: #52c41a;
        }

        .status.offline {
            background: #fff2f0;
            color: #f5222d;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #5a9178;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-link {
            text-align: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e1e1e1;
        }

        .demo-link a {
            color: #5a9178;
            text-decoration: none;
            font-weight: bold;
            padding: 12px 24px;
            border: 2px solid #5a9178;
            border-radius: 6px;
            display: inline-block;
            transition: all 0.3s;
        }

        .demo-link a:hover {
            background: #5a9178;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎱 Shuan-Q API 测试中心</h1>
        
        <div class="api-section">
            <div class="api-title">🏥 服务器状态检查</div>
            <div class="api-url">GET http://localhost:3000/health</div>
            <button class="test-button" onclick="testHealth()">检查服务器状态</button>
            <div id="health-status"></div>
            <div id="health-result" class="result" style="display: none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">📚 获取课程包列表</div>
            <div class="api-url">GET http://localhost:3000/api/packages</div>
            <button class="test-button" onclick="testPackages()">获取课程包</button>
            <button class="test-button" onclick="testPackagesWithParams()">带参数查询</button>
            <div id="packages-result" class="result" style="display: none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">👨‍🏫 获取教练列表</div>
            <div class="api-url">GET http://localhost:3000/api/coaches</div>
            <button class="test-button" onclick="testCoaches()">获取教练列表</button>
            <div id="coaches-result" class="result" style="display: none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">🔐 用户认证</div>
            <div class="api-url">POST http://localhost:3000/api/auth/send-code</div>
            <button class="test-button" onclick="testSendCode()">发送验证码</button>
            <button class="test-button" onclick="testLogin()">模拟登录</button>
            <div id="auth-result" class="result" style="display: none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">🛒 订单管理</div>
            <div class="api-url">POST http://localhost:3000/api/orders</div>
            <button class="test-button" onclick="testCreateOrder()">创建订单</button>
            <div id="orders-result" class="result" style="display: none;"></div>
        </div>

        <div class="demo-link">
            <a href="index.html">📱 查看移动端演示</a>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';

        // 通用API调用函数
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: true, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // 显示结果
        function showResult(elementId, result, loading = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (loading) {
                element.innerHTML = '<div class="loading"></div>请求中...';
                element.className = 'result';
                return;
            }
            
            if (result.success) {
                element.className = 'result success';
                element.innerHTML = `
                    <strong>✅ 请求成功 (${result.status || 200})</strong><br>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                element.className = 'result error';
                element.innerHTML = `
                    <strong>❌ 请求失败</strong><br>
                    <pre>${result.error}</pre>
                `;
            }
        }

        // 测试服务器健康状态
        async function testHealth() {
            showResult('health-result', null, true);
            const result = await apiCall(`${API_BASE}/health`);
            showResult('health-result', result);
            
            // 更新状态指示器
            const statusElement = document.getElementById('health-status');
            if (result.success) {
                statusElement.innerHTML = '<span class="status online">🟢 服务器在线</span>';
            } else {
                statusElement.innerHTML = '<span class="status offline">🔴 服务器离线</span>';
            }
        }

        // 测试课程包API
        async function testPackages() {
            showResult('packages-result', null, true);
            const result = await apiCall(`${API_BASE}/api/packages`);
            showResult('packages-result', result);
        }

        // 测试带参数的课程包查询
        async function testPackagesWithParams() {
            showResult('packages-result', null, true);
            const params = new URLSearchParams({
                page: 1,
                limit: 5,
                level: 'beginner'
            });
            const result = await apiCall(`${API_BASE}/api/packages?${params}`);
            showResult('packages-result', result);
        }

        // 测试教练API
        async function testCoaches() {
            showResult('coaches-result', null, true);
            const result = await apiCall(`${API_BASE}/api/coaches`);
            showResult('coaches-result', result);
        }

        // 测试发送验证码
        async function testSendCode() {
            showResult('auth-result', null, true);
            const result = await apiCall(`${API_BASE}/api/auth/send-code`, {
                method: 'POST',
                body: JSON.stringify({
                    phone: '13800138000'
                })
            });
            showResult('auth-result', result);
        }

        // 测试登录
        async function testLogin() {
            showResult('auth-result', null, true);
            const result = await apiCall(`${API_BASE}/api/auth/login`, {
                method: 'POST',
                body: JSON.stringify({
                    phone: '13800138000',
                    code: '123456'
                })
            });
            showResult('auth-result', result);
        }

        // 测试创建订单
        async function testCreateOrder() {
            showResult('orders-result', null, true);
            const result = await apiCall(`${API_BASE}/api/orders`, {
                method: 'POST',
                body: JSON.stringify({
                    packageId: 'package-1',
                    paymentMethod: 'wechat'
                }),
                headers: {
                    'Authorization': 'Bearer test-token'
                }
            });
            showResult('orders-result', result);
        }

        // 页面加载时自动检查服务器状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 API测试页面已加载');
            testHealth();
        });
    </script>
</body>
</html>
