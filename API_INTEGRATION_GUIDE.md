# 🔌 Shuan-Q API集成指南

## 📋 概述

本指南详细说明了Shuan-Q移动端应用如何与后端API进行集成，包括状态管理、错误处理和最佳实践。

## 🏗️ 架构概览

### Redux Toolkit + RTK Query
```
移动端应用
├── Redux Store
│   ├── authSlice (用户认证)
│   ├── coachSlice (教练管理)
│   ├── packageSlice (课程包管理)
│   └── orderSlice (订单管理)
└── API Services
    ├── 认证API
    ├── 教练API
    ├── 课程包API
    └── 订单API
```

## 🔐 认证系统

### authSlice 使用方法

#### 登录流程
```typescript
import { useDispatch, useSelector } from 'react-redux';
import { login } from '../store/slices/authSlice';

const LoginScreen = () => {
  const dispatch = useDispatch();
  const { isAuthenticated, loading, error } = useSelector(state => state.auth);

  const handleLogin = async (phone: string, code: string) => {
    try {
      const result = await dispatch(login({ phone, code }));
      if (result.payload) {
        // 登录成功，自动跳转
        navigation.navigate('Main');
      }
    } catch (error) {
      Alert.alert('错误', '登录失败');
    }
  };
};
```

#### 获取用户信息
```typescript
const { user, isAuthenticated } = useSelector(state => state.auth);

// 检查用户角色
const isCoach = user?.userType === 'coach';
const isStudent = user?.userType === 'student';
```

#### 退出登录
```typescript
import { logout } from '../store/slices/authSlice';

const handleLogout = () => {
  dispatch(logout());
  navigation.navigate('Login');
};
```

## 👨‍🏫 教练管理

### coachSlice API调用

#### 获取教练列表
```typescript
import { fetchCoaches } from '../store/slices/coachSlice';

const CoachListScreen = () => {
  const dispatch = useDispatch();
  const { coaches, loading, error } = useSelector(state => state.coach);

  useEffect(() => {
    // 基础查询
    dispatch(fetchCoaches());
    
    // 带参数查询
    dispatch(fetchCoaches({
      page: 1,
      limit: 10,
      search: '张教练',
      experience: '3-5',
      rating: '4+',
      sortBy: 'rating',
      sortOrder: 'DESC'
    }));
  }, []);
};
```

#### 获取教练详情
```typescript
import { fetchCoachById, selectCoach } from '../store/slices/coachSlice';

const CoachDetailScreen = () => {
  const { coachId } = route.params;
  const { selectedCoach, loading } = useSelector(state => state.coach);

  useEffect(() => {
    dispatch(fetchCoachById(coachId));
  }, [coachId]);

  // 或者从列表中选择
  const handleSelectCoach = (coach) => {
    dispatch(selectCoach(coach));
  };
};
```

### 教练数据结构
```typescript
interface Coach {
  id: string;
  user?: {
    id: string;
    nickname: string;
    phone: string;
  };
  specialties?: string[];
  experience?: number;
  rating?: number;
  hourlyRate?: number;
  bio?: string;
  studentCount?: number;
  createdAt: string;
  updatedAt: string;
}
```

## 📚 课程包管理

### packageSlice API调用

#### 获取课程包列表
```typescript
import { fetchPackages } from '../store/slices/packageSlice';

const PackageListScreen = () => {
  const dispatch = useDispatch();
  const { packages, loading, pagination } = useSelector(state => state.packages);

  const loadPackages = (params = {}) => {
    dispatch(fetchPackages({
      page: 1,
      limit: 10,
      category: 'basic',
      level: 'beginner',
      coachId: 'coach-id',
      search: '台球',
      sortBy: 'price',
      sortOrder: 'ASC',
      ...params
    }));
  };

  // 分页加载
  const loadMore = () => {
    if (pagination.page < pagination.totalPages) {
      loadPackages({ page: pagination.page + 1 });
    }
  };
};
```

#### 获取课程包详情
```typescript
import { fetchPackageById } from '../store/slices/packageSlice';

const PackageDetailScreen = () => {
  const { packageId } = route.params;
  const { currentPackage, loading } = useSelector(state => state.packages);

  useEffect(() => {
    dispatch(fetchPackageById(packageId));
  }, [packageId]);
};
```

### 课程包数据结构
```typescript
interface CoursePackage {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  totalSessions: number;
  validityDays: number;
  level: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  coach?: Coach;
  createdAt: string;
  updatedAt: string;
}
```

## 🛒 订单管理

### orderSlice API调用

#### 创建订单
```typescript
import { createOrder } from '../store/slices/orderSlice';

const handleCreateOrder = async (packageId: string) => {
  try {
    const result = await dispatch(createOrder({
      packageId,
      paymentMethod: 'wechat'
    }));
    
    if (result.payload) {
      navigation.navigate('OrderDetail', { orderId: result.payload.id });
    }
  } catch (error) {
    Alert.alert('错误', '创建订单失败');
  }
};
```

#### 获取我的订单
```typescript
import { fetchMyOrders } from '../store/slices/orderSlice';

const MyOrdersScreen = () => {
  const { orders, loading, pagination } = useSelector(state => state.orders);

  const loadOrders = (params = {}) => {
    dispatch(fetchMyOrders({
      page: 1,
      limit: 10,
      status: 'pending', // pending, paid, cancelled, refunded
      ...params
    }));
  };
};
```

#### 支付订单
```typescript
import { payOrder } from '../store/slices/orderSlice';

const handlePayOrder = async (orderId: string) => {
  try {
    await dispatch(payOrder(orderId));
    Alert.alert('成功', '支付成功');
    // 刷新订单列表
    dispatch(fetchMyOrders());
  } catch (error) {
    Alert.alert('错误', '支付失败');
  }
};
```

### 订单数据结构
```typescript
interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  packageId: string;
  amount: number;
  status: 'pending' | 'paid' | 'cancelled' | 'refunded';
  paymentMethod?: string;
  paidAt?: string;
  package?: CoursePackage;
  createdAt: string;
  updatedAt: string;
}
```

## 🔄 状态管理最佳实践

### 1. 错误处理
```typescript
const MyComponent = () => {
  const { loading, error } = useSelector(state => state.packages);

  useEffect(() => {
    if (error) {
      Alert.alert('错误', error);
      // 清除错误状态
      dispatch(clearError());
    }
  }, [error]);

  if (loading) {
    return <LoadingSpinner />;
  }
};
```

### 2. 加载状态
```typescript
const LoadingWrapper = ({ loading, children }) => {
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#5a9178" />
        <Text>加载中...</Text>
      </View>
    );
  }
  return children;
};
```

### 3. 数据缓存
```typescript
// 避免重复请求
const CoachListScreen = () => {
  const { coaches, loading } = useSelector(state => state.coach);

  useEffect(() => {
    // 只在数据为空时请求
    if (coaches.length === 0 && !loading) {
      dispatch(fetchCoaches());
    }
  }, []);
};
```

## 🌐 API配置

### 基础配置
```typescript
// src/services/api.ts
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api' 
  : 'https://api.shuanq.com/api';

export const apiConfig = {
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};
```

### 请求拦截器
```typescript
// 添加认证token
const getAuthHeaders = (getState) => {
  const { auth } = getState();
  return auth.token ? {
    'Authorization': `Bearer ${auth.token}`
  } : {};
};
```

### 响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
```

## 🔧 调试技巧

### 1. Redux DevTools
```typescript
// 在开发环境启用Redux DevTools
const store = configureStore({
  reducer: rootReducer,
  devTools: __DEV__,
});
```

### 2. 网络请求日志
```typescript
// 在开发环境打印API请求
if (__DEV__) {
  console.log('API Request:', {
    url,
    method,
    params,
    headers
  });
}
```

### 3. 状态变化监听
```typescript
// 监听特定状态变化
useEffect(() => {
  console.log('Packages state changed:', packages);
}, [packages]);
```

## 🚨 错误处理策略

### 1. 网络错误
```typescript
const handleNetworkError = (error) => {
  if (error.code === 'NETWORK_ERROR') {
    Alert.alert('网络错误', '请检查网络连接');
  } else if (error.code === 'TIMEOUT') {
    Alert.alert('请求超时', '请稍后重试');
  }
};
```

### 2. 认证错误
```typescript
const handleAuthError = (error) => {
  if (error.status === 401) {
    // Token过期，重新登录
    dispatch(logout());
    navigation.navigate('Login');
  }
};
```

### 3. 业务错误
```typescript
const handleBusinessError = (error) => {
  switch (error.code) {
    case 'PACKAGE_NOT_FOUND':
      Alert.alert('错误', '课程包不存在');
      break;
    case 'INSUFFICIENT_BALANCE':
      Alert.alert('余额不足', '请充值后重试');
      break;
    default:
      Alert.alert('错误', error.message);
  }
};
```

## 📊 性能优化

### 1. 数据预加载
```typescript
// 在应用启动时预加载关键数据
const App = () => {
  useEffect(() => {
    // 预加载热门课程包
    dispatch(fetchPackages({ limit: 6 }));
    // 预加载教练列表
    dispatch(fetchCoaches({ limit: 10 }));
  }, []);
};
```

### 2. 分页加载
```typescript
const useInfiniteScroll = (loadMore, hasMore) => {
  const handleEndReached = useCallback(() => {
    if (hasMore && !loading) {
      loadMore();
    }
  }, [hasMore, loading, loadMore]);

  return { handleEndReached };
};
```

### 3. 缓存策略
```typescript
// 设置数据过期时间
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟

const shouldRefreshData = (lastFetchTime) => {
  return Date.now() - lastFetchTime > CACHE_DURATION;
};
```

通过遵循这些API集成指南，你可以高效地管理应用的数据流和状态！🚀
