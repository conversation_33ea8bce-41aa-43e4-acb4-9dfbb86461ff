<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>an-Q 移動端開發成果展示</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }

        .showcase-header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .showcase-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .showcase-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .devices-container {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        /* 手機模擬器 */
        .phone-mockup {
            width: 300px;
            height: 600px;
            background: #000;
            border-radius: 25px;
            padding: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
            transform: scale(0.9);
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }

        /* 狀態欄 */
        .status-bar {
            background: #000;
            color: #fff;
            padding: 8px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            font-weight: 500;
        }

        /* 導航欄 */
        .navbar {
            background: linear-gradient(135deg, #5a9178, #4a7c59);
            color: #fff;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar h1 {
            font-size: 18px;
            font-weight: 600;
        }

        /* 主要內容區域 */
        .main-content {
            height: calc(100% - 120px);
            overflow-y: auto;
            background: #f8f9fa;
        }

        /* 標籤頁 */
        .tab-bar {
            display: flex;
            background: #fff;
            border-bottom: 1px solid #e9ecef;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .tab-item {
            flex: 1;
            padding: 12px 8px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .tab-item.active {
            color: #5a9178;
            border-bottom-color: #5a9178;
            background: rgba(90, 145, 120, 0.05);
        }

        .tab-item i {
            display: block;
            font-size: 18px;
            margin-bottom: 4px;
        }

        .tab-item span {
            font-size: 11px;
            font-weight: 500;
        }

        /* 內容頁面 */
        .tab-content {
            display: none;
            padding: 15px;
            height: calc(100% - 60px);
            overflow-y: auto;
        }

        .tab-content.active {
            display: block;
        }

        /* 卡片樣式 */
        .card {
            background: #fff;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .card-title {
            font-size: 15px;
            font-weight: 600;
            color: #333;
            line-height: 1.3;
        }

        .badge {
            background: #5a9178;
            color: #fff;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .badge.discount {
            background: #e74c3c;
        }

        .badge.level {
            background: #007bff;
        }

        .badge.hot {
            background: #ff6b35;
        }

        /* 價格樣式 */
        .price-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 8px 0;
        }

        .price-original {
            text-decoration: line-through;
            color: #999;
            font-size: 12px;
        }

        .price-current {
            color: #e74c3c;
            font-size: 18px;
            font-weight: bold;
        }

        /* 按鈕樣式 */
        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #5a9178, #4a7c59);
            color: #fff;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(90, 145, 120, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #5a9178;
            border: 1px solid #5a9178;
        }

        /* 首頁專用樣式 */
        .banner {
            background: linear-gradient(135deg, #5a9178, #4a7c59);
            color: white;
            padding: 20px 15px;
            text-align: center;
            margin-bottom: 15px;
        }

        .banner h2 {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .banner p {
            font-size: 13px;
            opacity: 0.9;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .action-btn {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 15px 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .action-btn:hover {
            border-color: #5a9178;
            color: #5a9178;
        }

        .action-btn i {
            font-size: 24px;
            margin-bottom: 5px;
            display: block;
        }

        .action-btn span {
            font-size: 12px;
            font-weight: 500;
        }

        /* 統計信息 */
        .stats {
            display: flex;
            justify-content: space-around;
            background: #fff;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #5a9178;
        }

        .stat-label {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
        }

        /* 教練卡片 */
        .coach-avatar {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: linear-gradient(135deg, #5a9178, #4a7c59);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 10px;
            flex-shrink: 0;
        }

        .coach-info {
            flex: 1;
        }

        .coach-name {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .coach-detail {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }

        .coach-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .rating {
            color: #ffc107;
            font-size: 12px;
            margin-right: 5px;
        }

        /* 功能特色展示 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin-top: 40px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            color: white;
            backdrop-filter: blur(10px);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .feature-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .feature-desc {
            font-size: 0.9rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .devices-container {
                flex-direction: column;
                align-items: center;
            }
            
            .phone-mockup {
                transform: scale(1);
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }

        /* 動畫效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .phone-mockup {
            animation: fadeInUp 0.8s ease-out;
        }

        .feature-card {
            animation: fadeInUp 0.8s ease-out;
        }

        .feature-card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .feature-card:nth-child(3) {
            animation-delay: 0.4s;
        }

        .feature-card:nth-child(4) {
            animation-delay: 0.6s;
        }
    </style>
</head>
<body>
    <div class="showcase-header">
        <h1>🎱 Shuan-Q 移動端開發成果</h1>
        <p>專業台球教練預約平台 - 移動端完整功能展示</p>
    </div>

    <div class="devices-container">
        <!-- 手機模擬器 -->
        <div class="phone-mockup">
            <div class="phone-screen">
                <!-- 狀態欄 -->
                <div class="status-bar">
                    <span>9:41</span>
                    <span>📶 🔋100%</span>
                </div>

                <!-- 導航欄 -->
                <div class="navbar">
                    <h1>Shuan-Q</h1>
                    <i class="fas fa-search"></i>
                </div>

                <!-- 主要內容 -->
                <div class="main-content">
                    <!-- 標籤欄 -->
                    <div class="tab-bar">
                        <button class="tab-item active" onclick="switchTab('home')">
                            <i class="fas fa-home"></i>
                            <span>首頁</span>
                        </button>
                        <button class="tab-item" onclick="switchTab('coaches')">
                            <i class="fas fa-user-tie"></i>
                            <span>教練</span>
                        </button>
                        <button class="tab-item" onclick="switchTab('packages')">
                            <i class="fas fa-box"></i>
                            <span>課程</span>
                        </button>
                        <button class="tab-item" onclick="switchTab('profile')">
                            <i class="fas fa-user"></i>
                            <span>我的</span>
                        </button>
                    </div>

                    <!-- 首頁內容 -->
                    <div id="home" class="tab-content active">
                        <div class="banner">
                            <h2>歡迎來到Shuan-Q</h2>
                            <p>找到最適合您的台球教練</p>
                        </div>

                        <div class="stats">
                            <div class="stat-item">
                                <div class="stat-number">168</div>
                                <div class="stat-label">專業教練</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">2.5K</div>
                                <div class="stat-label">學員</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">8.9K</div>
                                <div class="stat-label">完成課程</div>
                            </div>
                        </div>

                        <div class="quick-actions">
                            <div class="action-btn">
                                <i class="fas fa-calendar-plus"></i>
                                <span>預約課程</span>
                            </div>
                            <div class="action-btn">
                                <i class="fas fa-search"></i>
                                <span>找教練</span>
                            </div>
                            <div class="action-btn">
                                <i class="fas fa-history"></i>
                                <span>我的預約</span>
                            </div>
                            <div class="action-btn">
                                <i class="fas fa-star"></i>
                                <span>熱門課程</span>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">🔥 本週熱門教練</div>
                                <div class="badge hot">HOT</div>
                            </div>
                            <div class="coach-row">
                                <div class="coach-avatar">李</div>
                                <div class="coach-info">
                                    <div class="coach-name">李志明 教練</div>
                                    <div class="coach-detail">
                                        <span class="rating">⭐⭐⭐⭐⭐</span>
                                        專業8年 | ￥200/小時
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 教練列表 -->
                    <div id="coaches" class="tab-content">
                        <div class="card">
                            <div class="coach-row">
                                <div class="coach-avatar">王</div>
                                <div class="coach-info">
                                    <div class="coach-name">王大師</div>
                                    <div class="coach-detail">
                                        <span class="rating">⭐⭐⭐⭐⭐</span>
                                        15年經驗 | 專業九球
                                    </div>
                                </div>
                            </div>
                            <div class="price-container">
                                <span class="price-current">￥300</span>
                                <span>/小時</span>
                            </div>
                            <button class="btn btn-primary">立即預約</button>
                        </div>

                        <div class="card">
                            <div class="coach-row">
                                <div class="coach-avatar">張</div>
                                <div class="coach-info">
                                    <div class="coach-name">張教練</div>
                                    <div class="coach-detail">
                                        <span class="rating">⭐⭐⭐⭐⭐</span>
                                        10年經驗 | 中式台球專家
                                    </div>
                                </div>
                            </div>
                            <div class="price-container">
                                <span class="price-original">￥280</span>
                                <span class="price-current">￥220</span>
                                <span>/小時</span>
                            </div>
                            <button class="btn btn-primary">立即預約</button>
                        </div>

                        <div class="card">
                            <div class="coach-row">
                                <div class="coach-avatar">劉</div>
                                <div class="coach-info">
                                    <div class="coach-name">劉冠軍</div>
                                    <div class="coach-detail">
                                        <span class="rating">⭐⭐⭐⭐⭐</span>
                                        12年經驗 | 斯諾克高手
                                    </div>
                                </div>
                            </div>
                            <div class="price-container">
                                <span class="price-current">￥350</span>
                                <span>/小時</span>
                            </div>
                            <button class="btn btn-primary">立即預約</button>
                        </div>
                    </div>

                    <!-- 課程包 -->
                    <div id="packages" class="tab-content">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">初學者套餐</div>
                                <div class="badge level">入門</div>
                            </div>
                            <div class="coach-detail" style="margin-bottom: 10px;">
                                10節課程 | 基礎技巧 | 送球具
                            </div>
                            <div class="price-container">
                                <span class="price-original">￥2000</span>
                                <span class="price-current">￥1680</span>
                            </div>
                            <button class="btn btn-primary">立即購買</button>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">進階提升包</div>
                                <div class="badge">進階</div>
                            </div>
                            <div class="coach-detail" style="margin-bottom: 10px;">
                                15節課程 | 技巧提升 | 戰術指導
                            </div>
                            <div class="price-container">
                                <span class="price-current">￥2800</span>
                            </div>
                            <button class="btn btn-primary">立即購買</button>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">專業競技包</div>
                                <div class="badge discount">限時</div>
                            </div>
                            <div class="coach-detail" style="margin-bottom: 10px;">
                                20節課程 | 比賽訓練 | 一對一指導
                            </div>
                            <div class="price-container">
                                <span class="price-original">￥4500</span>
                                <span class="price-current">￥3999</span>
                            </div>
                            <button class="btn btn-primary">立即購買</button>
                        </div>
                    </div>

                    <!-- 個人中心 -->
                    <div id="profile" class="tab-content">
                        <div class="card">
                            <div class="coach-row">
                                <div class="coach-avatar">用</div>
                                <div class="coach-info">
                                    <div class="coach-name">用戶名稱</div>
                                    <div class="coach-detail">
                                        VIP會員 | 學習中
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-title">我的預約</div>
                            <div class="coach-detail" style="margin: 10px 0;">
                                下次課程：明天 14:00<br>
                                教練：王大師<br>
                                地點：台球俱樂部A
                            </div>
                            <button class="btn btn-outline">查看詳情</button>
                        </div>

                        <div class="card">
                            <div class="card-title">學習進度</div>
                            <div class="stats" style="margin: 10px 0;">
                                <div class="stat-item">
                                    <div class="stat-number">12</div>
                                    <div class="stat-label">已完成</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">3</div>
                                    <div class="stat-label">進行中</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">85%</div>
                                    <div class="stat-label">完成率</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能特色展示 -->
        <div class="features-grid">
            <div class="feature-card">
                <i class="fas fa-mobile-alt feature-icon"></i>
                <div class="feature-title">響應式設計</div>
                <div class="feature-desc">完美適配各種移動設備，提供一致的用戶體驗</div>
            </div>

            <div class="feature-card">
                <i class="fas fa-user-friends feature-icon"></i>
                <div class="feature-title">教練管理</div>
                <div class="feature-desc">完整的教練信息展示，評價系統和預約功能</div>
            </div>

            <div class="feature-card">
                <i class="fas fa-calendar-check feature-icon"></i>
                <div class="feature-title">智能預約</div>
                <div class="feature-desc">時間衝突檢測，自動提醒，靈活的預約管理</div>
            </div>

            <div class="feature-card">
                <i class="fas fa-credit-card feature-icon"></i>
                <div class="feature-title">支付系統</div>
                <div class="feature-desc">安全的訂單處理，多種支付方式，完整交易記錄</div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabName) {
            // 隱藏所有標籤內容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有標籤的活躍狀態
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 顯示選中的標籤內容
            document.getElementById(tabName).classList.add('active');
            
            // 設置選中標籤為活躍狀態
            event.target.closest('.tab-item').classList.add('active');
        }

        // 添加點擊效果
        document.addEventListener('DOMContentLoaded', function() {
            // 為所有按鈕添加點擊效果
            document.querySelectorAll('.btn, .action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // 自動輪播演示
            let currentTab = 0;
            const tabs = ['home', 'coaches', 'packages', 'profile'];
            
            setInterval(() => {
                if (document.querySelector('.tab-item.active').onclick !== null) return; // 如果用戶正在交互，不自動切換
                
                currentTab = (currentTab + 1) % tabs.length;
                
                // 切換標籤
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                document.querySelectorAll('.tab-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                document.getElementById(tabs[currentTab]).classList.add('active');
                document.querySelectorAll('.tab-item')[currentTab].classList.add('active');
            }, 5000); // 每5秒切換一次
        });
    </script>
</body>
</html> 