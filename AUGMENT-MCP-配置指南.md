# Augment 中配置 MCP Feedback Enhanced 详细指南

## 🎯 Augment 简介

**Augment** 是一个基于 VS Code 的 AI 编程助手，支持 MCP (Model Context Protocol) 工具集成。通过配置 mcp-feedback-enhanced，你可以让 AI 在执行操作前征求你的确认。

## 🔧 在 Augment 中配置 MCP

### 方法一：通过 VS Code 设置界面配置

#### 步骤 1: 打开 Augment 设置
1. 在 VS Code 中按 `Cmd+,` (macOS) 或 `Ctrl+,` (Windows/Linux)
2. 在搜索框中输入 `augment mcp`
3. 找到 **"Augment: Mcp Servers"** 设置项

#### 步骤 2: 添加 MCP 服务器配置
点击 **"Edit in settings.json"** 或 **"Add Item"**，然后添加以下配置：

**Web UI 模式 (推荐用于远程开发):**
```json
{
  "augment.mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_PORT": "8765"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

**桌面应用模式 (推荐用于本地开发):**
```json
{
  "augment.mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DESKTOP_MODE": "true",
        "MCP_WEB_PORT": "8765",
        "MCP_DEBUG": "false"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### 方法二：直接编辑 settings.json 文件

#### 步骤 1: 打开 settings.json
1. 按 `Cmd+Shift+P` (macOS) 或 `Ctrl+Shift+P` (Windows/Linux)
2. 输入 `Preferences: Open Settings (JSON)`
3. 选择并打开 settings.json 文件

#### 步骤 2: 添加配置
在 settings.json 文件中添加或修改以下内容：

```json
{
  "augment.mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_PORT": "8765"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

## 🔍 验证配置是否成功

### 步骤 1: 重启 VS Code
配置完成后，完全关闭并重新打开 VS Code。

### 步骤 2: 检查 MCP 工具状态
1. 打开 Augment 面板
2. 查看工具列表，应该能看到 `mcp-feedback-enhanced` 工具
3. 工具状态应该显示为绿色（已连接）

### 步骤 3: 测试工具功能
在 Augment 中输入以下测试请求：
```
请使用 mcp-feedback-enhanced 工具询问我是否同意继续。
```

如果配置成功，应该会弹出反馈界面。

## 🚨 常见问题和解决方案

### 问题 1: 工具显示为红色（未连接）

**可能原因:**
- uv 命令不在 PATH 中
- mcp-feedback-enhanced 未正确安装

**解决方案:**
```bash
# 确保 uv 在 PATH 中
export PATH="$HOME/.local/bin:$PATH"

# 测试 mcp-feedback-enhanced
uvx mcp-feedback-enhanced@latest version
```

### 问题 2: 找不到 "Augment: Mcp Servers" 设置

**解决方案:**
1. 确保 Augment 扩展已安装并启用
2. 更新 Augment 扩展到最新版本
3. 直接编辑 settings.json 文件

### 问题 3: 配置后工具不显示

**解决方案:**
1. 检查 JSON 语法是否正确
2. 重启 VS Code
3. 查看 VS Code 开发者控制台的错误信息

### 问题 4: 权限问题

**解决方案:**
```bash
# macOS/Linux
chmod +x ~/.local/bin/uvx

# 修复 npm 权限
sudo chown -R $(whoami) ~/.npm
```

## 🎯 针对 Shuan-Q 项目的特殊配置

### 项目特定环境变量
```json
{
  "augment.mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_PORT": "8765",
        "PROJECT_NAME": "Shuan-Q",
        "NODE_ENV": "development"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### 工作区特定配置
在项目根目录创建 `.vscode/settings.json`:
```json
{
  "augment.mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_PORT": "8765"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

## 🔧 高级配置选项

### 调试模式
```json
{
  "augment.mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "true",
        "MCP_WEB_PORT": "8765"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### 自定义端口
```json
{
  "augment.mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_PORT": "9999"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

## 📱 使用示例

配置完成后，你可以这样使用：

```
用户: "帮我安装 react-native-vector-icons 依赖"

AI: 我准备为你安装 react-native-vector-icons 依赖。让我先通过 mcp-feedback-enhanced 征求你的确认。

[弹出反馈界面]
- 操作: npm install react-native-vector-icons
- 影响: 修改 package.json，可能需要原生代码链接
- 是否继续？[确认] [取消]
```

## 🎉 配置完成检查清单

- [ ] ✅ Augment 扩展已安装
- [ ] ✅ uv 已安装并在 PATH 中
- [ ] ✅ mcp-feedback-enhanced 已测试可用
- [ ] ✅ settings.json 配置已添加
- [ ] ✅ VS Code 已重启
- [ ] ✅ MCP 工具显示为绿色状态
- [ ] ✅ 测试反馈功能正常

## 📞 获取帮助

如果遇到问题：
1. 查看 VS Code 开发者控制台 (`Help > Toggle Developer Tools`)
2. 检查 Augment 扩展日志
3. 参考 `MCP-FEEDBACK-ENHANCED-安装指南.md`
4. 访问 GitHub 项目页面

---

**🎉 恭喜！现在你可以在 Augment 中安全地使用 AI 辅助开发了！**
