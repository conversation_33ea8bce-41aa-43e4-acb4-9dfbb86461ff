import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { TabParamList } from './types';

// 导入Stack导航器
import HomeStackNavigator from './HomeStackNavigator';
import MallStackNavigator from './MallStackNavigator';
import CoachStackNavigator from './CoachStackNavigator';
import ProfileStackNavigator from './ProfileStackNavigator';

const Tab = createBottomTabNavigator<TabParamList>();

const TabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Mall':
              iconName = 'shopping-cart';
              break;
            case 'Coach':
              iconName = 'person';
              break;
            case 'Profile':
              iconName = 'account-circle';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#5a9178',
        tabBarInactiveTintColor: '#8e8e93',
        tabBarStyle: {
          backgroundColor: '#ffffff',
          borderTopWidth: 1,
          borderTopColor: '#e1e1e1',
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeStackNavigator}
        options={{
          tabBarLabel: '首页',
        }}
      />
      <Tab.Screen 
        name="Mall" 
        component={MallStackNavigator}
        options={{
          tabBarLabel: '商城',
        }}
      />
      <Tab.Screen 
        name="Coach" 
        component={CoachStackNavigator}
        options={{
          tabBarLabel: '教练',
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileStackNavigator}
        options={{
          tabBarLabel: '我的',
        }}
      />
    </Tab.Navigator>
  );
};

export default TabNavigator;
