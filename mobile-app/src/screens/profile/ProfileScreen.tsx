import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootState } from '../../store';
import { logout } from '../../store/slices/authSlice';

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);

  const handleLogin = () => {
    navigation.navigate('Login' as never);
  };

  const handleLogout = () => {
    Alert.alert(
      '确认退出',
      '确定要退出登录吗？',
      [
        { text: '取消', style: 'cancel' },
        { 
          text: '确定', 
          onPress: () => {
            dispatch(logout());
            Alert.alert('提示', '已退出登录');
          }
        },
      ]
    );
  };

  const handleMenuPress = (screen: string) => {
    if (!isAuthenticated) {
      Alert.alert('提示', '请先登录', [
        { text: '取消', style: 'cancel' },
        { text: '去登录', onPress: handleLogin },
      ]);
      return;
    }
    navigation.navigate(screen as never);
  };

  // 学员菜单项
  const studentMenuItems = [
    { icon: 'shopping-bag', title: '我的订单', screen: 'MyOrders', color: '#f5222d' },
    { icon: 'event', title: '我的预约', screen: 'MyAppointments', color: '#52c41a' },
    { icon: 'favorite', title: '我的收藏', screen: 'MyFavorites', color: '#faad14' },
    { icon: 'history', title: '学习记录', screen: 'StudyHistory', color: '#722ed1' },
  ];

  // 教练菜单项
  const coachMenuItems = [
    { icon: 'person', title: '教练资料', screen: 'CoachProfile', color: '#5a9178' },
    { icon: 'school', title: '课程包管理', screen: 'ManagePackages', color: '#1890ff' },
    { icon: 'schedule', title: '预约管理', screen: 'ManageAppointments', color: '#52c41a' },
    { icon: 'people', title: '学员管理', screen: 'StudentManagement', color: '#faad14' },
    { icon: 'assessment', title: '收入统计', screen: 'IncomeStats', color: '#f5222d' },
  ];

  // 通用菜单项
  const commonMenuItems = [
    { icon: 'edit', title: '编辑资料', screen: 'EditProfile', color: '#666' },
    { icon: 'settings', title: '设置', screen: 'Settings', color: '#666' },
    { icon: 'help', title: '帮助与反馈', screen: 'Help', color: '#666' },
  ];

  const renderMenuItem = (item: any) => (
    <TouchableOpacity
      key={item.title}
      style={styles.menuItem}
      onPress={() => handleMenuPress(item.screen)}
    >
      <View style={[styles.menuIcon, { backgroundColor: `${item.color}20` }]}>
        <Icon name={item.icon} size={24} color={item.color} />
      </View>
      <Text style={styles.menuTitle}>{item.title}</Text>
      <Icon name="chevron-right" size={20} color="#999" />
    </TouchableOpacity>
  );

  if (!isAuthenticated) {
    return (
      <View style={styles.container}>
        <View style={styles.loginPrompt}>
          <View style={styles.avatarPlaceholder}>
            <Icon name="person" size={48} color="#999" />
          </View>
          <Text style={styles.loginTitle}>欢迎使用 Shuan-Q</Text>
          <Text style={styles.loginSubtitle}>登录后享受更多功能</Text>
          <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
            <Text style={styles.loginButtonText}>立即登录</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const isCoach = user?.userType === 'coach';
  const menuItems = isCoach ? coachMenuItems : studentMenuItems;

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 用户信息头部 */}
        <View style={styles.userHeader}>
          <View style={styles.userAvatar}>
            <Icon name="person" size={40} color="#5a9178" />
          </View>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{user?.nickname}</Text>
            <Text style={styles.userPhone}>{user?.phone}</Text>
            <View style={styles.userTypeBadge}>
              <Text style={styles.userTypeText}>
                {isCoach ? '教练' : '学员'}
              </Text>
            </View>
          </View>
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => handleMenuPress('EditProfile')}
          >
            <Icon name="edit" size={20} color="#5a9178" />
          </TouchableOpacity>
        </View>

        {/* 角色专用菜单 */}
        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>
            {isCoach ? '教练功能' : '我的服务'}
          </Text>
          {menuItems.map(renderMenuItem)}
        </View>

        {/* 通用菜单 */}
        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>通用功能</Text>
          {commonMenuItems.map(renderMenuItem)}
        </View>

        {/* 退出登录 */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Icon name="exit-to-app" size={20} color="#f5222d" />
          <Text style={styles.logoutText}>退出登录</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  loginPrompt: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  loginTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  loginSubtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 32,
    textAlign: 'center',
  },
  loginButton: {
    backgroundColor: '#5a9178',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 24,
  },
  loginButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    marginBottom: 12,
  },
  userAvatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#f0f8f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  userPhone: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  userTypeBadge: {
    backgroundColor: '#5a9178',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    alignSelf: 'flex-start',
  },
  userTypeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  editButton: {
    padding: 8,
  },
  menuSection: {
    backgroundColor: '#fff',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 14,
    color: '#999',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#f8f9fa',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuTitle: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    paddingVertical: 16,
    marginTop: 12,
    marginBottom: 32,
  },
  logoutText: {
    fontSize: 16,
    color: '#f5222d',
    marginLeft: 8,
  },
});

export default ProfileScreen;
