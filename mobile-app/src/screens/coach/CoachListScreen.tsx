import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootState } from '../../store';
import { fetchCoaches, selectCoach } from '../../store/slices/coachSlice';

// 导入组件
import SearchBar from '../../components/SearchBar';
import CoachCard from '../../components/CoachCard';

const CoachListScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const [refreshing, setRefreshing] = useState(false);
  const [showFilter, setShowFilter] = useState(false);

  const { coaches, loading, error } = useSelector((state: RootState) => state.coach);

  useEffect(() => {
    loadCoaches();
  }, []);

  const loadCoaches = async () => {
    try {
      await dispatch(fetchCoaches() as any);
    } catch (error) {
      Alert.alert('错误', '加载教练列表失败');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCoaches();
    setRefreshing(false);
  };

  const handleSearch = (query: string) => {
    navigation.navigate('CoachSearch' as never, { query });
  };

  const handleCoachPress = (coach: any) => {
    dispatch(selectCoach(coach));
    navigation.navigate('CoachDetail' as never, { coachId: coach.id });
  };

  const renderCoachItem = ({ item }: { item: any }) => (
    <CoachCard
      coach={item}
      onPress={() => handleCoachPress(item)}
    />
  );

  const renderHeader = () => (
    <View>
      {/* 搜索栏 */}
      <View style={styles.searchContainer}>
        <SearchBar
          placeholder="搜索教练..."
          onSearch={handleSearch}
          onFocus={() => navigation.navigate('CoachSearch' as never)}
          editable={false}
        />
      </View>

      {/* 筛选和排序 */}
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilter(true)}
        >
          <Icon name="filter-list" size={20} color="#5a9178" />
          <Text style={styles.filterText}>筛选</Text>
        </TouchableOpacity>

        <View style={styles.sortInfo}>
          <Text style={styles.sortText}>
            共 {coaches.length} 位教练
          </Text>
        </View>
      </View>
    </View>
  );

  if (loading && coaches.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#5a9178" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={coaches}
        renderItem={renderCoachItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: '#5a9178',
  },
  listContainer: {
    paddingBottom: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#5a9178',
    borderRadius: 16,
  },
  filterText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#5a9178',
  },
  sortInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortText: {
    fontSize: 14,
    color: '#666',
  },
});

export default CoachListScreen;
