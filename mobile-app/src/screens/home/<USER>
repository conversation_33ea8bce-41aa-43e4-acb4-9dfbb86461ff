import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  RefreshControl,
  FlatList,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootState } from '../../store';
import { fetchPackages } from '../../store/slices/packageSlice';

// 导入组件
import SearchBar from '../../components/SearchBar';
import Banner from '../../components/Banner';
import ProductCard from '../../components/ProductCard';
import QuickActions from '../../components/QuickActions';

const { width } = Dimensions.get('window');

const HomeScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const [refreshing, setRefreshing] = useState(false);
  
  const { packages, loading } = useSelector((state: RootState) => state.packages);
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  // 轮播图数据
  const bannerData = [
    {
      id: '1',
      image: 'https://via.placeholder.com/400x200/5a9178/ffffff?text=专业台球教学',
      title: '专业台球教学',
      subtitle: '一对一精品课程',
    },
    {
      id: '2',
      image: 'https://via.placeholder.com/400x200/4a7c59/ffffff?text=新手入门',
      title: '新手入门课程',
      subtitle: '零基础也能快速上手',
    },
    {
      id: '3',
      image: 'https://via.placeholder.com/400x200/6ba085/ffffff?text=进阶提升',
      title: '进阶提升训练',
      subtitle: '技术突破，水平提升',
    },
  ];

  // 快速入口数据
  const quickActions = [
    {
      id: '1',
      icon: 'person',
      title: '找教练',
      subtitle: '专业教练',
      onPress: () => navigation.navigate('Coach' as never),
    },
    {
      id: '2',
      icon: 'school',
      title: '课程包',
      subtitle: '精品课程',
      onPress: () => navigation.navigate('Mall' as never),
    },
    {
      id: '3',
      icon: 'event',
      title: '我的预约',
      subtitle: '预约管理',
      onPress: () => {
        if (isAuthenticated) {
          navigation.navigate('Profile' as never, { screen: 'MyAppointments' });
        } else {
          navigation.navigate('Login' as never);
        }
      },
    },
    {
      id: '4',
      icon: 'shopping-bag',
      title: '我的订单',
      subtitle: '订单查询',
      onPress: () => {
        if (isAuthenticated) {
          navigation.navigate('Profile' as never, { screen: 'MyOrders' });
        } else {
          navigation.navigate('Login' as never);
        }
      },
    },
  ];

  useEffect(() => {
    loadHotPackages();
  }, []);

  const loadHotPackages = async () => {
    try {
      await dispatch(fetchPackages({ limit: 6 }) as any);
    } catch (error) {
      console.error('加载热门课程包失败:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHotPackages();
    setRefreshing(false);
  };

  const handleSearch = (query: string) => {
    navigation.navigate('Search' as never, { type: 'product', query });
  };

  const renderPackageItem = ({ item }: { item: any }) => (
    <ProductCard
      package={item}
      onPress={() => navigation.navigate('PackageDetail' as never, { packageId: item.id })}
    />
  );

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* 搜索栏 */}
        <View style={styles.searchContainer}>
          <SearchBar
            placeholder="搜索课程包、教练..."
            onSearch={handleSearch}
          />
        </View>

        {/* 轮播图 */}
        <Banner data={bannerData} />

        {/* 快速入口 */}
        <QuickActions data={quickActions} />

        {/* 热门课程包 */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>热门课程包</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('Mall' as never)}
            >
              <Text style={styles.moreText}>更多 →</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={packages.slice(0, 6)}
            renderItem={renderPackageItem}
            keyExtractor={(item) => item.id}
            numColumns={2}
            scrollEnabled={false}
            contentContainerStyle={styles.packageGrid}
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: '#5a9178',
  },
  section: {
    marginTop: 16,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  moreText: {
    fontSize: 14,
    color: '#5a9178',
  },
  packageGrid: {
    paddingBottom: 16,
  },
});

export default HomeScreen;
