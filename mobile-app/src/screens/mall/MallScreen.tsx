import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootState } from '../../store';
import { fetchPackages } from '../../store/slices/packageSlice';

// 导入组件
import SearchBar from '../../components/SearchBar';
import Banner from '../../components/Banner';
import ProductCard from '../../components/ProductCard';
import CategorySelector from '../../components/CategorySelector';
import FilterModal from '../../components/FilterModal';

const MallScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  
  const [refreshing, setRefreshing] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filters, setFilters] = useState({
    level: 'all',
    priceRange: 'all',
    sortBy: 'createdAt',
    sortOrder: 'DESC',
  });

  const { packages, loading, pagination } = useSelector((state: RootState) => state.packages);

  // 商城轮播图数据
  const bannerData = [
    {
      id: '1',
      image: 'https://via.placeholder.com/400x200/5a9178/ffffff?text=精品课程包',
      title: '精品课程包',
      subtitle: '专业教练，优质服务',
    },
    {
      id: '2',
      image: 'https://via.placeholder.com/400x200/4a7c59/ffffff?text=限时优惠',
      title: '限时优惠',
      subtitle: '新用户专享折扣',
    },
  ];

  // 分类数据
  const categories = [
    { id: 'all', name: '全部', icon: 'apps' },
    { id: 'basic', name: '基础教学', icon: 'school' },
    { id: 'advanced', name: '进阶训练', icon: 'trending-up' },
    { id: 'technique', name: '技巧提升', icon: 'star' },
    { id: 'competition', name: '比赛培训', icon: 'emoji-events' },
  ];

  useEffect(() => {
    loadPackages();
  }, [selectedCategory, filters]);

  const loadPackages = async (page = 1) => {
    try {
      const params: any = {
        page,
        limit: 10,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
      };

      if (selectedCategory !== 'all') {
        params.category = selectedCategory;
      }

      if (filters.level !== 'all') {
        params.level = filters.level;
      }

      await dispatch(fetchPackages(params) as any);
    } catch (error) {
      console.error('加载课程包失败:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPackages(1);
    setRefreshing(false);
  };

  const handleSearch = (query: string) => {
    navigation.navigate('ProductSearch' as never, { query });
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  const handleFilterApply = (newFilters: any) => {
    setFilters(newFilters);
    setShowFilter(false);
  };

  const renderPackageItem = ({ item }: { item: any }) => (
    <ProductCard
      package={item}
      onPress={() => navigation.navigate('PackageDetail' as never, { packageId: item.id })}
    />
  );

  const renderHeader = () => (
    <View>
      {/* 搜索栏 */}
      <View style={styles.searchContainer}>
        <SearchBar
          placeholder="搜索课程包..."
          onSearch={handleSearch}
          onFocus={() => navigation.navigate('ProductSearch' as never)}
          editable={false}
        />
      </View>

      {/* 轮播图 */}
      <Banner data={bannerData} />

      {/* 分类选择器 */}
      <CategorySelector
        categories={categories}
        selectedCategory={selectedCategory}
        onCategorySelect={handleCategorySelect}
      />

      {/* 筛选和排序 */}
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilter(true)}
        >
          <Icon name="filter-list" size={20} color="#5a9178" />
          <Text style={styles.filterText}>筛选</Text>
        </TouchableOpacity>
        
        <View style={styles.sortInfo}>
          <Text style={styles.sortText}>
            共 {pagination.total} 个课程包
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={packages}
        renderItem={renderPackageItem}
        keyExtractor={(item) => item.id}
        numColumns={2}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />

      {/* 筛选弹窗 */}
      <FilterModal
        visible={showFilter}
        filters={filters}
        onApply={handleFilterApply}
        onClose={() => setShowFilter(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: '#5a9178',
  },
  listContainer: {
    paddingBottom: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#5a9178',
    borderRadius: 16,
  },
  filterText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#5a9178',
  },
  sortInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortText: {
    fontSize: 14,
    color: '#666',
  },
});

export default MallScreen;
